# ProChart组件NG统计时间显示修复

## 问题描述
用户希望NG统计在使用ProChart组件时，非对比状态下切换成表格状态时，也能像产能统计那样正常显示时间信息。虽然NG统计的x轴不是时间（而是NG类型），但仍需要在表格中显示时间列。

## 修复内容

### 1. 表格列生成逻辑修复
- 修改了`tableColumns`计算逻辑，在非对比模式下，当x轴不是时间时（如NG类型），添加时间列
- 代码位置：`src/components/ProChart/index.vue` 第249-256行

### 2. 表格数据格式化修复
- 在`tableData`计算中添加了对非时间轴的特殊处理
- 当x轴不是时间时，为time字段添加时间信息，x轴显示原值（如NG类型）
- 时间轴的处理保持原有逻辑
- 代码位置：`src/components/ProChart/index.vue` 第326-368行

### 3. 导出数据格式化修复
- 在`exportChartData`函数中添加了对非时间轴的处理
- 根据x轴类型确定表头：非时间轴包含"时间"和x轴名称两列
- 数据行处理：非时间轴先添加时间信息，再添加x轴值
- 代码位置：`src/components/ProChart/index.vue` 第496-581行

## 修复逻辑

### x轴类型判断
```javascript
if (xAxis.name && xAxis.name !== "时间") {
  // 非时间轴处理（如NG类型）
} else {
  // 时间轴处理
}
```

### 处理逻辑
1. **时间轴**：进行时间格式化，添加年月日信息
2. **非时间轴**（如NG类型）：
   - 表格列：添加独立的时间列 + x轴列
   - 数据处理：time字段显示时间信息，xAxis字段显示原值
   - 导出：包含时间列和分类列

## 测试场景
1. NG统计非对比模式下切换到表格状态 - 应显示时间列和NG类型列
2. 产能capacity等时间轴统计 - 保持原有时间显示逻辑
3. 导出功能 - 正确处理不同类型的表头和数据

## 预期结果
- NG统计表格显示时间列（查询的时间范围）和NG类型列
- 产能等其他统计类型的时间显示保持正常
- 导出功能根据x轴类型正确生成表头和数据
- 实现了用户期望的"NG统计也能像产能那样显示时间"的需求
