# ProChart组件智能时间跨度自动判断

## 修改概述

根据用户需求，ProChart组件现在内部根据时间跨度自动计算时间显示格式，不再需要外部传入timeMode参数。

## 时间跨度判断规则

### 规则说明

- **一天内**：hour模式 - 显示具体日期
- **超过一天但≤3个月**：month模式 - 显示日期范围  
- **超过3个月**：year模式 - 显示月份范围

### 显示格式

| 时间跨度 | 模式 | 显示格式 | 示例 |
|---------|------|---------|------|
| **≤ 1天** | hour | 具体日期 | `2024-01-15` |
| **> 1天 且 ≤ 3个月** | month | 日期范围 | `2024-01-01 ~ 2024-01-31` |
| **> 3个月** | year | 月份范围 | `2024-01 ~ 2024-12` |

## 核心实现逻辑

### 计算代码

```javascript
// 计算时间跨度
const startMoment = moment(startTime);
const endMoment = moment(endTime);
const daysDiff = endMoment.diff(startMoment, "days");
const monthsDiff = endMoment.diff(startMoment, "months");

// 根据时间跨度自动决定显示格式
if (daysDiff <= 1) {
  // 一天内：hour模式 - 显示具体日期
  timeRange = startMoment.format("YYYY-MM-DD");
} else if (daysDiff > 1 && monthsDiff <= 3) {
  // 超过一天但小于等于3个月：month模式 - 显示日期范围
  timeRange = `${startMoment.format("YYYY-MM-DD")} ~ ${endMoment.format("YYYY-MM-DD")}`;
} else {
  // 超过3个月：year模式 - 显示月份范围
  timeRange = `${startMoment.format("YYYY-MM")} ~ ${endMoment.format("YYYY-MM")}`;
}
```

## 修改位置

### 1. 表格数据处理（第310-320行）
- 修改了`tableData`计算中的时间格式化逻辑
- 简化了判断条件，移除了超过一年的特殊处理

### 2. 导出数据处理（第559-569行）
- 修改了`exportChartData`函数中非时间轴的时间格式化逻辑
- 保持与表格数据相同的处理逻辑

### 3. 移除timeMode参数
- 移除了props中的timeMode参数定义
- 移除了相关的默认值设置

## 应用场景

### 自动适配示例

1. **日查询**（2024-01-15 ~ 2024-01-15）
   - 时间跨度：0天
   - 显示：`2024-01-15`

2. **周查询**（2024-01-01 ~ 2024-01-07）
   - 时间跨度：6天
   - 显示：`2024-01-01 ~ 2024-01-07`

3. **月查询**（2024-01-01 ~ 2024-01-31）
   - 时间跨度：30天
   - 显示：`2024-01-01 ~ 2024-01-31`

4. **季度查询**（2024-01-01 ~ 2024-03-31）
   - 时间跨度：90天（3个月）
   - 显示：`2024-01-01 ~ 2024-03-31`

5. **半年查询**（2024-01-01 ~ 2024-06-30）
   - 时间跨度：181天（6个月）
   - 显示：`2024-01 ~ 2024-06`

## 优势

1. **自动化**：无需手动配置，根据实际时间跨度自动选择最合适的显示格式
2. **简化使用**：组件使用者不需要关心timeMode参数
3. **一致性**：表格显示、对比模式、导出功能都使用相同的逻辑
4. **智能化**：根据数据特点自动优化显示效果

## 兼容性

- 保持了所有现有功能不变
- NG统计、capacity等组件无需修改即可享受新功能
- 向后兼容，不影响现有代码

## 测试建议

1. 测试不同时间跨度的查询，验证显示格式是否正确
2. 测试NG统计的表格模式，确认时间列正常显示
3. 测试capacity等组件，确认时间格式符合预期
4. 测试导出功能，确认导出的时间格式正确
